# VLESS-REALITY 支持文档

## 🎉 新增支持

我们已经完全支持 **VLESS-XTLS-uTLS-REALITY** 节点，这是目前最先进的代理协议配置。

## 🔧 支持的特性

### VLESS 协议增强
- ✅ **REALITY 安全传输** - 最新的反审查技术
- ✅ **XTLS Flow 控制** - 支持 `xtls-rprx-vision` 等流控
- ✅ **uTLS 指纹伪装** - 支持 Chrome、Firefox、Safari 等指纹
- ✅ **多种传输协议** - TCP、WebSocket、gRPC、HTTP/2

### 解析参数支持
- `security=reality` - REALITY 安全传输
- `flow=xtls-rprx-vision` - XTLS 流控
- `fp=chrome` - uTLS 指纹
- `pbk=PublicKey` - REALITY 公钥
- `sid=ShortId` - REALITY 短 ID
- `sni=domain.com` - SNI 域名
- `type=tcp/ws/grpc/h2` - 传输协议

## 📋 VLESS-REALITY 链接格式

### 标准格式
```
vless://UUID@SERVER:PORT?encryption=none&flow=FLOW&security=reality&sni=SNI&fp=FINGERPRINT&pbk=PUBLICKEY&sid=SHORTID&type=NETWORK#NAME
```

### 示例链接
```
vless://<EMAIL>:443?encryption=none&flow=xtls-rprx-vision&security=reality&sni=www.microsoft.com&fp=chrome&pbk=SomePublicKey&sid=12345678&type=tcp&headerType=none#VLESS-REALITY-节点
```

## 🎯 Clash 配置生成

### 生成的 Clash 代理配置
```yaml
- name: "VLESS-REALITY-节点"
  type: vless
  server: example.com
  port: 443
  uuid: 12345678-1234-1234-1234-123456789012
  packet-encoding: xudp
  flow: xtls-rprx-vision
  tls: true
  reality: true
  servername: www.microsoft.com
  fingerprint: chrome
  reality-opts:
    public-key: SomePublicKey
    short-id: "12345678"
  network: tcp
```

## 🔄 修复的问题

### 1. 代理组配置错误
**问题**: `proxy group[1]: '🚀 节点选择' '♻️ 自动选择' not found`

**原因**: 
- 节点解析失败导致代理列表为空
- 代理组引用了不存在的代理名称

**解决方案**:
- ✅ 完善了 VLESS-REALITY 解析器
- ✅ 改进了代理组生成逻辑
- ✅ 添加了错误处理和验证

### 2. REALITY 参数支持
**问题**: 原有解析器不支持 REALITY 相关参数

**解决方案**:
- ✅ 添加了完整的 REALITY 参数解析
- ✅ 支持 uTLS 指纹伪装
- ✅ 支持 XTLS 流控配置

### 3. Clash 兼容性
**问题**: 生成的配置在 Clash 中报错

**解决方案**:
- ✅ 使用标准的 Clash VLESS 配置格式
- ✅ 正确处理 REALITY 选项
- ✅ 确保所有必要字段都存在

## 🧪 测试验证

### 支持的 VLESS 变体
1. **VLESS + TCP + REALITY**
2. **VLESS + WebSocket + REALITY**
3. **VLESS + gRPC + REALITY**
4. **VLESS + HTTP/2 + REALITY**
5. **VLESS + TCP + TLS** (传统模式)

### Flow 控制支持
- `xtls-rprx-vision`
- `xtls-rprx-vision-udp443`
- `xtls-rprx-origin`
- `xtls-rprx-origin-udp443`

### 指纹伪装支持
- `chrome` (默认)
- `firefox`
- `safari`
- `ios`
- `android`
- `edge`
- `360`
- `qq`
- `random`
- `randomized`

## 📱 使用方法

### 1. 添加 VLESS-REALITY 节点
1. 在管理面板中选择 "VLess" 节点类型
2. 输入节点名称
3. 粘贴完整的 VLESS-REALITY 链接
4. 点击"添加节点"

### 2. 生成 Clash 订阅
1. 添加节点后，复制 Clash 订阅链接
2. 在 Clash 客户端中导入订阅
3. 选择对应的节点进行连接

### 3. 验证配置
- 检查 Clash 日志是否有错误
- 测试节点连接是否正常
- 确认 REALITY 伪装是否生效

## ⚠️ 注意事项

### REALITY 配置要求
1. **公钥 (pbk)**: 必须与服务端配置一致
2. **SNI 域名**: 建议使用大型网站域名 (如 microsoft.com, apple.com)
3. **短 ID (sid)**: 可选，用于进一步混淆
4. **指纹 (fp)**: 建议使用 chrome 以获得最佳兼容性

### 客户端兼容性
- ✅ **Clash Verge** - 完全支持
- ✅ **Clash for Windows** - 需要较新版本
- ✅ **ClashX Pro** - 支持 REALITY
- ⚠️ **旧版 Clash** - 可能不支持 REALITY

## 🔍 故障排除

### 常见错误
1. **"reality not supported"** - 客户端版本过旧
2. **"invalid public key"** - 公钥格式错误
3. **"connection failed"** - SNI 域名或端口错误

### 调试步骤
1. 检查节点链接格式是否正确
2. 验证所有必要参数是否存在
3. 确认客户端支持 REALITY
4. 查看客户端详细日志

## 📈 性能优化

### 推荐配置
- **传输协议**: TCP (最佳性能)
- **Flow 控制**: `xtls-rprx-vision` (推荐)
- **指纹伪装**: `chrome` (最佳兼容性)
- **SNI 域名**: 选择地理位置接近的大型网站

### 高级选项
- 启用 UDP 支持: `packet-encoding: xudp`
- 优化延迟: 使用合适的 `tolerance` 值
- 负载均衡: 配置多个 REALITY 节点

## 🚀 未来计划

- 支持更多 REALITY 高级特性
- 添加自动 SNI 域名检测
- 优化 XTLS 流控配置
- 支持 REALITY 多端口配置

---

现在你的订阅转换器已经完全支持 VLESS-XTLS-uTLS-REALITY 节点了！🎉
