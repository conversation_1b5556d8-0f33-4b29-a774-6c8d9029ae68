/**
 * 测试Clash配置生成
 */

// 模拟NodeConverter类
class TestNodeConverter {
  // 转换为Clash配置
  toClashConfig(nodes) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map(p => p.name);

    // 如果没有有效的代理节点，创建一个基础配置
    if (proxyNames.length === 0) {
      return {
        port: 7890,
        'socks-port': 7891,
        'allow-lan': false,
        mode: 'rule',
        'log-level': 'info',
        'external-controller': '127.0.0.1:9090',
        proxies: [],
        'proxy-groups': [
          {
            name: '🎯 全球直连',
            type: 'select',
            proxies: ['DIRECT']
          }
        ],
        rules: [
          'DOMAIN-SUFFIX,local,DIRECT',
          'IP-CIDR,*********/8,DIRECT',
          'IP-CIDR,**********/12,DIRECT',
          'IP-CIDR,***********/16,DIRECT',
          'IP-CIDR,10.0.0.0/8,DIRECT',
          'GEOIP,CN,🎯 全球直连',
          'MATCH,🎯 全球直连'
        ]
      };
    }

    // 有代理节点时的完整配置
    const proxyGroups = [
      {
        name: '🚀 节点选择',
        type: 'select',
        proxies: ['♻️ 自动选择', '🎯 全球直连'].concat(proxyNames)
      },
      {
        name: '🎯 全球直连',
        type: 'select',
        proxies: ['DIRECT']
      }
    ];

    // 只有在有多个节点时才添加自动选择组
    if (proxyNames.length > 1) {
      proxyGroups.splice(1, 0, {
        name: '♻️ 自动选择',
        type: 'url-test',
        proxies: proxyNames,
        url: 'http://www.gstatic.com/generate_204',
        interval: 300,
        tolerance: 50
      });
    }

    return {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: proxies,
      'proxy-groups': proxyGroups,
      rules: [
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        'GEOIP,CN,🎯 全球直连',
        'MATCH,🚀 节点选择'
      ]
    };
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    switch (node.type) {
      case 'vmess':
        return {
          name: node.name,
          type: 'vmess',
          server: node.server,
          port: node.port,
          uuid: node.uuid,
          alterId: node.alterId || 0,
          cipher: node.cipher || 'auto',
          network: node.network || 'tcp',
          tls: node.tls || false
        };
      case 'ss':
        return {
          name: node.name,
          type: 'ss',
          server: node.server,
          port: node.port,
          cipher: node.method,
          password: node.password,
          udp: true
        };
      case 'trojan':
        return {
          name: node.name,
          type: 'trojan',
          server: node.server,
          port: node.port,
          password: node.password,
          udp: true,
          sni: node.sni
        };
      default:
        return null;
    }
  }

  // 简单的JSON到YAML转换器
  jsonToYaml(obj, indent = 0) {
    const spaces = '  '.repeat(indent);
    let yaml = '';

    if (Array.isArray(obj)) {
      for (const item of obj) {
        if (typeof item === 'object' && item !== null) {
          yaml += `${spaces}- ${this.jsonToYaml(item, indent + 1).trim()}\n`;
        } else {
          yaml += `${spaces}- ${this.escapeYamlValue(item)}\n`;
        }
      }
    } else if (typeof obj === 'object' && obj !== null) {
      for (const [key, value] of Object.entries(obj)) {
        if (Array.isArray(value)) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else if (typeof value === 'object' && value !== null) {
          yaml += `${spaces}${key}:\n`;
          yaml += this.jsonToYaml(value, indent + 1);
        } else {
          yaml += `${spaces}${key}: ${this.escapeYamlValue(value)}\n`;
        }
      }
    }

    return yaml;
  }

  // 转义YAML值
  escapeYamlValue(value) {
    if (typeof value === 'string') {
      // 如果字符串包含特殊字符，需要加引号
      if (value.includes(':') || value.includes('#') || value.includes('[') || 
          value.includes(']') || value.includes('{') || value.includes('}') ||
          value.includes('|') || value.includes('>') || value.includes('&') ||
          value.includes('*') || value.includes('!') || value.includes('%') ||
          value.includes('@') || value.includes('`')) {
        return `"${value.replace(/"/g, '\\"')}"`;
      }
      return value;
    }
    return value;
  }
}

// 测试数据
const testNodes = [
  {
    type: 'vmess',
    name: '香港节点01',
    server: 'hk.example.com',
    port: 443,
    uuid: '12345678-1234-1234-1234-123456789012',
    alterId: 0,
    cipher: 'auto',
    network: 'ws',
    tls: true,
    path: '/path',
    host: 'hk.example.com'
  },
  {
    type: 'ss',
    name: '美国节点01',
    server: 'us.example.com',
    port: 8388,
    method: 'aes-256-gcm',
    password: 'password123'
  }
];

// 运行测试
console.log('🧪 测试Clash配置生成...\n');

const converter = new TestNodeConverter();

// 测试空节点列表
console.log('📋 测试空节点列表:');
const emptyConfig = converter.toClashConfig([]);
console.log('✅ 空配置生成成功');
console.log(`   - 代理组数量: ${emptyConfig['proxy-groups'].length}`);
console.log(`   - 代理数量: ${emptyConfig.proxies.length}`);
console.log('');

// 测试有节点的配置
console.log('📋 测试有节点的配置:');
const fullConfig = converter.toClashConfig(testNodes);
console.log('✅ 完整配置生成成功');
console.log(`   - 代理组数量: ${fullConfig['proxy-groups'].length}`);
console.log(`   - 代理数量: ${fullConfig.proxies.length}`);
console.log('');

// 测试YAML转换
console.log('📋 测试YAML转换:');
const yamlContent = converter.jsonToYaml(fullConfig);
console.log('✅ YAML转换成功');
console.log(`   - YAML长度: ${yamlContent.length} 字符`);
console.log('');

// 输出部分YAML内容用于检查
console.log('📄 YAML配置预览:');
console.log('==================');
console.log(yamlContent.substring(0, 500) + '...');
console.log('==================');

console.log('\n✅ 所有测试完成！');
console.log('\n💡 修复内容:');
console.log('- ✅ 修复了空节点列表时的proxy-groups配置错误');
console.log('- ✅ 添加了完整的代理配置字段');
console.log('- ✅ 实现了JSON到YAML的转换');
console.log('- ✅ 优化了代理组的逻辑判断');
