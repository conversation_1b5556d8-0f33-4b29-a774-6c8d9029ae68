# 快速开始指南

## 🚀 5分钟部署到Cloudflare

### 第一步：准备环境

1. 确保你有 [Cloudflare账户](https://dash.cloudflare.com/sign-up)
2. 安装 [Node.js](https://nodejs.org/) (版本 16+)

### 第二步：克隆并安装

```bash
# 克隆项目
git clone <your-repo-url>
cd sub_converter

# 安装依赖
npm install
```

### 第三步：一键部署

```bash
# 运行自动部署脚本
npm run setup
```

这个脚本会自动：
- 检查并安装 Wrangler CLI
- 提示你登录 Cloudflare（如果未登录）
- 创建 KV 命名空间
- 更新配置文件
- 部署到 Cloudflare Workers

### 第四步：配置认证

编辑 `wrangler.toml` 文件，修改用户名和密码：

```toml
[vars]
ADMIN_USERNAME = "your-username"    # 改为你的用户名
ADMIN_PASSWORD = "your-password"    # 改为你的密码
```

然后重新部署：

```bash
npm run deploy
```

### 第五步：开始使用

1. 访问你的 Worker 域名（部署完成后会显示）
2. 使用配置的用户名密码登录
3. 开始添加节点！

## 📱 使用示例

### 添加VMess节点

1. 选择节点类型：VMess
2. 输入节点名称：香港节点01
3. 粘贴VMess链接：
   ```
   vmess://eyJ2IjoiMiIsInBzIjoi6aaZ5rivMDEiLCJhZGQiOiJoay5leGFtcGxlLmNvbSIsInBvcnQiOiI0NDMiLCJpZCI6IjEyMzQ1Njc4LTEyMzQtMTIzNC0xMjM0LTEyMzQ1Njc4OTBhYiIsImFpZCI6IjAiLCJzY3kiOiJhdXRvIiwibmV0Ijoid3MiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiJoay5leGFtcGxlLmNvbSIsInBhdGgiOiIvcGF0aCIsInRscyI6InRscyJ9
   ```

### 添加Shadowsocks节点

1. 选择节点类型：Shadowsocks
2. 输入节点名称：美国节点01
3. 粘贴SS链接：
   ```
   ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@us.example.com:8388#美国节点01
   ```

### 获取订阅链接

添加节点后，在管理面板中可以看到三种订阅链接：

- **V2Ray订阅**：适用于 V2RayN、V2RayNG 等客户端
- **Clash订阅**：适用于 Clash for Windows、ClashX 等客户端  
- **Surge订阅**：适用于 Surge 客户端

点击"复制"按钮即可复制订阅链接到剪贴板。

## 🔧 常用命令

```bash
# 本地开发（在本地测试）
npm run dev

# 部署到生产环境
npm run deploy

# 查看运行日志
npm run tail

# 运行测试
npm run test
```

## ⚠️ 重要提示

1. **修改默认密码**：部署后务必修改 `wrangler.toml` 中的默认用户名和密码
2. **保护订阅链接**：订阅链接包含你的节点信息，请妥善保管
3. **定期备份**：建议定期导出节点数据进行备份
4. **遵守法律**：请确保在当地法律允许的范围内使用

## 🆘 遇到问题？

### 常见问题

**Q: 部署时提示 "KV namespace not found"**
A: 确保 `wrangler.toml` 中的 KV 命名空间 ID 正确，或重新运行 `npm run setup`

**Q: 登录失败**
A: 检查 `wrangler.toml` 中的用户名密码是否正确，修改后需要重新部署

**Q: 节点解析失败**
A: 检查节点链接格式是否正确，确保选择了正确的节点类型

**Q: 订阅链接无法访问**
A: 确保 Worker 已成功部署，检查域名是否正确

### 获取帮助

- 查看详细文档：`README.md`
- 查看运行日志：`npm run tail`
- 提交问题：[GitHub Issues](your-repo-issues-url)

## 🎉 完成！

现在你已经成功部署了自己的订阅转换服务！享受使用吧！
