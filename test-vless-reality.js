/**
 * 测试VLESS-REALITY节点解析和Clash配置生成
 */

// 模拟NodeConverter类
class TestNodeConverter {
  // 解析VLess链接
  parseVless(config) {
    if (config.startsWith('vless://')) {
      const url = new URL(config);
      const params = url.searchParams;
      
      // 基础配置
      const node = {
        type: 'vless',
        name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
        server: url.hostname,
        port: parseInt(url.port),
        uuid: url.username,
        encryption: params.get('encryption') || 'none',
        network: params.get('type') || 'tcp'
      };

      // 安全传输配置
      const security = params.get('security');
      if (security === 'tls') {
        node.tls = true;
        node.sni = params.get('sni') || url.hostname;
        node.alpn = params.get('alpn');
        node.fingerprint = params.get('fp');
      } else if (security === 'reality') {
        node.reality = true;
        node.sni = params.get('sni') || url.hostname;
        node.fingerprint = params.get('fp') || 'chrome';
        node.publicKey = params.get('pbk');
        node.shortId = params.get('sid');
        node.spiderX = params.get('spx');
      }

      // Flow控制 (XTLS)
      const flow = params.get('flow');
      if (flow) {
        node.flow = flow;
      }

      // 传输协议配置
      switch (node.network) {
        case 'tcp':
          node.headerType = params.get('headerType') || 'none';
          break;
        case 'ws':
          node.path = params.get('path') || '/';
          node.host = params.get('host') || '';
          break;
        case 'grpc':
          node.serviceName = params.get('serviceName') || 'GunService';
          node.mode = params.get('mode') || 'gun';
          break;
        case 'h2':
          node.path = params.get('path') || '/';
          node.host = params.get('host') || '';
          break;
      }

      return node;
    }
    throw new Error('无效的VLess链接');
  }

  // 节点转Clash代理配置
  nodeToClashProxy(node) {
    if (node.type === 'vless') {
      const vlessProxy = {
        name: node.name,
        type: 'vless',
        server: node.server,
        port: node.port,
        uuid: node.uuid,
        'packet-encoding': 'xudp'
      };
      
      // 添加Flow控制 (XTLS)
      if (node.flow) {
        vlessProxy.flow = node.flow;
      }
      
      // 添加TLS配置
      if (node.tls) {
        vlessProxy.tls = true;
        if (node.sni) {
          vlessProxy.servername = node.sni;
        }
        if (node.alpn) {
          vlessProxy.alpn = node.alpn.split(',');
        }
        if (node.fingerprint) {
          vlessProxy.fingerprint = node.fingerprint;
        }
      }
      
      // 添加REALITY配置
      if (node.reality) {
        vlessProxy.tls = true;
        vlessProxy.reality = true;
        if (node.sni) {
          vlessProxy.servername = node.sni;
        }
        if (node.fingerprint) {
          vlessProxy.fingerprint = node.fingerprint;
        }
        if (node.publicKey) {
          vlessProxy['reality-opts'] = {
            'public-key': node.publicKey,
            'short-id': node.shortId || ''
          };
        }
      }
      
      // 添加网络配置
      if (node.network === 'ws') {
        vlessProxy.network = 'ws';
        vlessProxy['ws-opts'] = {
          path: node.path || '/',
          headers: node.host ? { Host: node.host } : {}
        };
      } else if (node.network === 'grpc') {
        vlessProxy.network = 'grpc';
        vlessProxy['grpc-opts'] = {
          'grpc-service-name': node.serviceName || 'GunService'
        };
      } else if (node.network === 'h2') {
        vlessProxy.network = 'h2';
        vlessProxy['h2-opts'] = {
          host: [node.host || node.server],
          path: node.path || '/'
        };
      } else {
        vlessProxy.network = 'tcp';
      }
      
      return vlessProxy;
    }
    return null;
  }

  // 转换为Clash配置
  toClashConfig(nodes) {
    const proxies = nodes.map(node => this.nodeToClashProxy(node)).filter(Boolean);
    const proxyNames = proxies.map(p => p.name);
    
    // 如果没有有效的代理节点，创建一个基础配置
    if (proxyNames.length === 0) {
      return {
        port: 7890,
        'socks-port': 7891,
        'allow-lan': false,
        mode: 'rule',
        'log-level': 'info',
        'external-controller': '127.0.0.1:9090',
        proxies: [],
        'proxy-groups': [
          {
            name: '🎯 全球直连',
            type: 'select',
            proxies: ['DIRECT']
          }
        ],
        rules: [
          'DOMAIN-SUFFIX,local,DIRECT',
          'IP-CIDR,*********/8,DIRECT',
          'IP-CIDR,**********/12,DIRECT',
          'IP-CIDR,***********/16,DIRECT',
          'IP-CIDR,10.0.0.0/8,DIRECT',
          'GEOIP,CN,🎯 全球直连',
          'MATCH,🎯 全球直连'
        ]
      };
    }
    
    // 有代理节点时的完整配置
    const proxyGroups = [
      {
        name: '🚀 节点选择',
        type: 'select',
        proxies: ['🎯 全球直连'].concat(proxyNames)
      },
      {
        name: '🎯 全球直连',
        type: 'select',
        proxies: ['DIRECT']
      }
    ];
    
    // 只有在有多个节点时才添加自动选择组
    if (proxyNames.length > 1) {
      proxyGroups.splice(1, 0, {
        name: '♻️ 自动选择',
        type: 'url-test',
        proxies: proxyNames,
        url: 'http://www.gstatic.com/generate_204',
        interval: 300,
        tolerance: 50
      });
      // 更新节点选择组
      proxyGroups[0].proxies = ['♻️ 自动选择', '🎯 全球直连'].concat(proxyNames);
    }
    
    return {
      port: 7890,
      'socks-port': 7891,
      'allow-lan': false,
      mode: 'rule',
      'log-level': 'info',
      'external-controller': '127.0.0.1:9090',
      proxies: proxies,
      'proxy-groups': proxyGroups,
      rules: [
        'DOMAIN-SUFFIX,local,DIRECT',
        'IP-CIDR,*********/8,DIRECT',
        'IP-CIDR,**********/12,DIRECT',
        'IP-CIDR,***********/16,DIRECT',
        'IP-CIDR,10.0.0.0/8,DIRECT',
        'GEOIP,CN,🎯 全球直连',
        'MATCH,🚀 节点选择'
      ]
    };
  }
}

// 测试VLESS-REALITY链接
const testVlessRealityLink = 'vless://<EMAIL>:443?encryption=none&flow=xtls-rprx-vision&security=reality&sni=www.microsoft.com&fp=chrome&pbk=SomePublicKey&sid=12345678&type=tcp&headerType=none#VLESS-REALITY-节点';

console.log('🧪 测试VLESS-REALITY节点解析和Clash配置生成...\n');

const converter = new TestNodeConverter();

try {
  // 测试节点解析
  console.log('📋 测试VLESS-REALITY节点解析:');
  console.log('链接:', testVlessRealityLink);
  console.log('');
  
  const node = converter.parseVless(testVlessRealityLink);
  console.log('✅ 节点解析成功:');
  console.log(`   - 名称: ${node.name}`);
  console.log(`   - 服务器: ${node.server}:${node.port}`);
  console.log(`   - UUID: ${node.uuid}`);
  console.log(`   - 网络: ${node.network}`);
  console.log(`   - 安全: ${node.reality ? 'REALITY' : (node.tls ? 'TLS' : 'none')}`);
  console.log(`   - Flow: ${node.flow || 'none'}`);
  console.log(`   - SNI: ${node.sni || 'none'}`);
  console.log(`   - 指纹: ${node.fingerprint || 'none'}`);
  console.log('');

  // 测试Clash代理配置生成
  console.log('📋 测试Clash代理配置生成:');
  const clashProxy = converter.nodeToClashProxy(node);
  console.log('✅ Clash代理配置生成成功:');
  console.log(JSON.stringify(clashProxy, null, 2));
  console.log('');

  // 测试完整Clash配置
  console.log('📋 测试完整Clash配置生成:');
  const clashConfig = converter.toClashConfig([node]);
  console.log('✅ 完整Clash配置生成成功:');
  console.log(`   - 代理数量: ${clashConfig.proxies.length}`);
  console.log(`   - 代理组数量: ${clashConfig['proxy-groups'].length}`);
  console.log('');

  // 检查代理组配置
  console.log('📋 检查代理组配置:');
  clashConfig['proxy-groups'].forEach((group, index) => {
    console.log(`   ${index + 1}. ${group.name}:`);
    console.log(`      - 类型: ${group.type}`);
    console.log(`      - 代理: [${group.proxies.join(', ')}]`);
  });
  console.log('');

  console.log('✅ 所有测试完成！VLESS-REALITY节点支持正常！');

} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error('详细错误:', error);
}

console.log('\n💡 支持的VLESS-REALITY特性:');
console.log('- ✅ REALITY安全传输');
console.log('- ✅ XTLS Flow控制');
console.log('- ✅ uTLS指纹伪装');
console.log('- ✅ 多种传输协议 (TCP, WebSocket, gRPC, HTTP/2)');
console.log('- ✅ 完整的Clash配置生成');
