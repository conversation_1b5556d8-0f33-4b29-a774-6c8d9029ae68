/**
 * 测试修复后的Clash配置格式
 */

const { NodeConverter } = require('./src/converter.js');

console.log('🧪 测试修复后的VLESS-REALITY配置格式...\n');

// 创建转换器实例
const converter = new NodeConverter();

// 测试用户提供的VLESS-REALITY链接
const testVlessLink = 'vless://7983e0bb-1ef9-49c5-c41b-e9d1d8040e0f@*************:56879?type=tcp&security=reality&sni=www.icloud.com&pbk=g1f1wLjim5gOVGnI5LGUV0dL4iFXPoiepOPZfSxJe14&flow=xtls-rprx-vision&fp=chrome#US-CloudSilk|<EMAIL>';

try {
  // 解析节点
  const node = converter.parseVless(testVlessLink);
  console.log('✅ 节点解析成功');
  console.log(`   名称: ${node.name}`);
  console.log(`   服务器: ${node.server}:${node.port}`);
  console.log(`   UUID: ${node.uuid}`);
  console.log(`   网络: ${node.network}`);
  console.log(`   安全: ${node.reality ? 'REALITY' : 'TLS'}`);
  console.log(`   Flow: ${node.flow}`);
  console.log(`   SNI: ${node.sni}`);
  console.log(`   指纹: ${node.fingerprint}`);
  console.log(`   公钥: ${node.publicKey?.substring(0, 20)}...`);

  // 生成Clash代理配置
  const clashProxy = converter.nodeToClashProxy(node);
  console.log('\n📋 生成的Clash代理配置:');
  console.log(JSON.stringify(clashProxy, null, 2));

  // 对比期望的配置格式
  console.log('\n📋 期望的配置格式:');
  const expectedConfig = {
    name: "US-CloudSilk|<EMAIL>|0GB|19700101",
    type: "vless",
    server: "*************",
    port: 56879,
    uuid: "b27ef2b2-2c95-4b33-805f-960505a7a810",
    cipher: "",
    alterId: 0,
    udp: true,
    tls: true,
    "skip-cert-verify": false,
    servername: "www.icloud.com",
    network: "tcp",
    flow: "xtls-rprx-vision",
    "client-fingerprint": "chrome",
    "reality-opts": {
      "public-key": "g1f1wLjim5gOVGnI5LGUV0dL4iFXPoiepOPZfSxJe14"
    }
  };
  console.log(JSON.stringify(expectedConfig, null, 2));

  // 检查字段匹配
  console.log('\n🔍 字段匹配检查:');
  const requiredFields = [
    'name', 'type', 'server', 'port', 'uuid', 'cipher', 'alterId', 
    'udp', 'tls', 'skip-cert-verify', 'servername', 'network', 
    'flow', 'client-fingerprint', 'reality-opts'
  ];

  let allFieldsMatch = true;
  for (const field of requiredFields) {
    const hasField = clashProxy.hasOwnProperty(field);
    console.log(`   ${hasField ? '✅' : '❌'} ${field}: ${hasField ? '存在' : '缺失'}`);
    if (!hasField) allFieldsMatch = false;
  }

  // 检查不应该存在的字段
  const unwantedFields = ['packet-encoding', 'reality', 'fingerprint'];
  for (const field of unwantedFields) {
    const hasField = clashProxy.hasOwnProperty(field);
    if (hasField) {
      console.log(`   ❌ ${field}: 不应该存在但存在了`);
      allFieldsMatch = false;
    } else {
      console.log(`   ✅ ${field}: 正确地不存在`);
    }
  }

  console.log(`\n${allFieldsMatch ? '🎉' : '⚠️'} 配置格式${allFieldsMatch ? '完全匹配' : '需要调整'}`);

  // 生成完整的Clash配置
  const fullConfig = converter.toClashConfig([node]);
  console.log('\n📊 完整配置统计:');
  console.log(`   代理数量: ${fullConfig.proxies.length}`);
  console.log(`   代理组数量: ${fullConfig['proxy-groups'].length}`);
  console.log(`   规则数量: ${fullConfig.rules.length}`);

} catch (error) {
  console.error('❌ 测试失败:', error.message);
}

console.log('\n✅ 测试完成！');
