#!/usr/bin/env node

/**
 * 部署脚本 - 帮助用户快速部署订阅转换器到Cloudflare Workers
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 订阅转换器部署脚本');
console.log('====================\n');

// 检查是否安装了wrangler
function checkWrangler() {
  try {
    execSync('npx wrangler --version', { stdio: 'ignore' });
    console.log('✅ Wrangler CLI 已安装');
  } catch (error) {
    console.log('❌ 未找到 Wrangler CLI');
    console.log('正在安装 Wrangler...');
    execSync('npm install wrangler', { stdio: 'inherit' });
    console.log('✅ Wrangler CLI 安装完成');
  }
}

// 检查是否已登录Cloudflare
function checkLogin() {
  try {
    execSync('npx wrangler whoami', { stdio: 'ignore' });
    console.log('✅ 已登录 Cloudflare');
    return true;
  } catch (error) {
    console.log('❌ 未登录 Cloudflare');
    return false;
  }
}

// 创建KV命名空间
function createKVNamespace() {
  console.log('\n📦 创建 KV 命名空间...');
  try {
    const result = execSync('npx wrangler kv:namespace create "NODES_KV"', { encoding: 'utf8' });
    console.log(result);
    
    // 提取命名空间ID
    const match = result.match(/id = "([^"]+)"/);
    if (match) {
      const namespaceId = match[1];
      console.log(`✅ KV 命名空间创建成功，ID: ${namespaceId}`);
      return namespaceId;
    }
  } catch (error) {
    console.log('❌ 创建 KV 命名空间失败');
    console.log('请手动创建 KV 命名空间并更新 wrangler.toml');
    return null;
  }
}

// 更新wrangler.toml配置
function updateWranglerConfig(namespaceId) {
  const wranglerPath = path.join(__dirname, 'wrangler.toml');
  
  if (!fs.existsSync(wranglerPath)) {
    console.log('❌ 未找到 wrangler.toml 文件');
    return false;
  }

  try {
    let content = fs.readFileSync(wranglerPath, 'utf8');
    
    if (namespaceId) {
      // 更新KV命名空间ID
      content = content.replace(/id = "your-kv-namespace-id"/, `id = "${namespaceId}"`);
      content = content.replace(/preview_id = "your-preview-kv-namespace-id"/, `preview_id = "${namespaceId}"`);
    }

    fs.writeFileSync(wranglerPath, content);
    console.log('✅ wrangler.toml 配置已更新');
    return true;
  } catch (error) {
    console.log('❌ 更新 wrangler.toml 失败:', error.message);
    return false;
  }
}

// 部署Worker
function deployWorker() {
  console.log('\n🚀 部署 Worker...');
  try {
    execSync('npx wrangler deploy', { stdio: 'inherit' });
    console.log('\n✅ 部署成功！');
    return true;
  } catch (error) {
    console.log('❌ 部署失败');
    return false;
  }
}

// 显示部署后的信息
function showPostDeployInfo() {
  console.log('\n🎉 部署完成！');
  console.log('================');
  console.log('\n📝 接下来的步骤：');
  console.log('1. 修改 wrangler.toml 中的用户名和密码');
  console.log('2. 访问你的 Worker 域名进行登录');
  console.log('3. 开始添加节点并生成订阅链接');
  console.log('\n🔧 有用的命令：');
  console.log('- npm run dev     # 本地开发');
  console.log('- npm run deploy  # 重新部署');
  console.log('- npm run tail    # 查看日志');
  console.log('\n📚 更多信息请查看 README.md');
}

// 主函数
async function main() {
  try {
    // 1. 检查wrangler
    checkWrangler();

    // 2. 检查登录状态
    if (!checkLogin()) {
      console.log('\n🔑 请先登录 Cloudflare：');
      console.log('运行命令: npx wrangler login');
      process.exit(1);
    }

    // 3. 创建KV命名空间
    const namespaceId = createKVNamespace();

    // 4. 更新配置
    updateWranglerConfig(namespaceId);

    // 5. 部署
    if (deployWorker()) {
      showPostDeployInfo();
    }

  } catch (error) {
    console.log('❌ 部署过程中出现错误:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}
