<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅转换器 - 管理面板</title>
    <style>{{CSS}}</style>
</head>
<body>
    <div class="dashboard-container">
        <h1>节点管理面板</h1>
        
        <!-- 添加节点表单 -->
        <div class="container">
            <h2>添加新节点</h2>
            <form method="POST" action="/add-node">
                <div class="form-group">
                    <label for="node-type">节点类型:</label>
                    <select id="node-type" name="type" required>
                        <option value="">选择节点类型</option>
                        <option value="vmess">VMess</option>
                        <option value="vless">VLess</option>
                        <option value="ss">Shadowsocks</option>
                        <option value="trojan">Trojan</option>
                        <option value="http">HTTP/HTTPS</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="node-name">节点名称:</label>
                    <input type="text" id="node-name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="node-config">节点配置 (链接或JSON):</label>
                    <textarea id="node-config" name="config" rows="4" required placeholder="粘贴节点链接或配置信息"></textarea>
                </div>
                <button type="submit">添加节点</button>
            </form>
        </div>

        <!-- 订阅链接 -->
        <div class="container subscription-links">
            <h2>订阅链接</h2>
            <div class="link-item">
                <span>V2Ray订阅:</span>
                <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/v2ray')">复制</button>
            </div>
            <div class="link-item">
                <span>Clash订阅:</span>
                <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/clash')">复制</button>
            </div>
            <div class="link-item">
                <span>Surge订阅:</span>
                <button class="copy-btn" onclick="copyToClipboard('{{BASE_URL}}/sub/surge')">复制</button>
            </div>
        </div>

        <!-- 节点列表 -->
        <div class="container">
            <h2>已添加的节点</h2>
            {{NODES_LIST}}
        </div>

        <!-- 退出登录 -->
        <div class="container">
            <form method="POST" action="/logout">
                <button type="submit" class="btn-secondary">退出登录</button>
            </form>
        </div>
    </div>

    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('链接已复制到剪贴板');
            });
        }

        function deleteNode(nodeId) {
            if (confirm('确定要删除这个节点吗？')) {
                fetch('/delete-node', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ id: nodeId })
                }).then(() => {
                    location.reload();
                });
            }
        }
    </script>
</body>
</html>
