/**
 * 测试用户提供的VLESS-REALITY链接
 */

// 用户提供的VLESS链接
const userVlessLink = 'vless://7983e0bb-1ef9-49c5-c41b-e9d1d8040e0f@185.148.13.75:56879?type=tcp&security=reality&sni=www.icloud.com&pbk=g1f1wLjim5gOVGnI5LGUV0dL4iFXPoiepOPZfSxJe14&flow=xtls-rprx-vision&fp=chrome#US-CloudSilk|<EMAIL>';

console.log('🧪 测试用户提供的VLESS-REALITY链接...\n');
console.log('原始链接:');
console.log(userVlessLink);
console.log('');

// 解析链接
try {
  const url = new URL(userVlessLink);
  const params = url.searchParams;
  
  console.log('📋 解析结果:');
  console.log(`UUID: ${url.username}`);
  console.log(`服务器: ${url.hostname}`);
  console.log(`端口: ${url.port}`);
  console.log(`名称: ${decodeURIComponent(url.hash.substring(1))}`);
  console.log('');
  
  console.log('📋 参数详情:');
  console.log(`type: ${params.get('type')}`);
  console.log(`security: ${params.get('security')}`);
  console.log(`sni: ${params.get('sni')}`);
  console.log(`pbk: ${params.get('pbk')}`);
  console.log(`flow: ${params.get('flow')}`);
  console.log(`fp: ${params.get('fp')}`);
  console.log(`encryption: ${params.get('encryption') || 'none (默认)'}`);
  console.log('');

  // 构建节点对象
  const node = {
    type: 'vless',
    name: decodeURIComponent(url.hash.substring(1)) || 'VLess节点',
    server: url.hostname,
    port: parseInt(url.port),
    uuid: url.username,
    encryption: params.get('encryption') || 'none',
    network: params.get('type') || 'tcp'
  };

  // 安全传输配置
  const security = params.get('security');
  if (security === 'reality') {
    node.reality = true;
    node.sni = params.get('sni') || url.hostname;
    node.fingerprint = params.get('fp') || 'chrome';
    node.publicKey = params.get('pbk');
    node.shortId = params.get('sid');
  }

  // Flow控制
  const flow = params.get('flow');
  if (flow) {
    node.flow = flow;
  }

  console.log('📋 构建的节点对象:');
  console.log(JSON.stringify(node, null, 2));
  console.log('');

  // 生成Clash配置
  const clashProxy = {
    name: node.name,
    type: 'vless',
    server: node.server,
    port: node.port,
    uuid: node.uuid,
    'packet-encoding': 'xudp'
  };

  // 添加Flow控制
  if (node.flow) {
    clashProxy.flow = node.flow;
  }

  // 添加REALITY配置
  if (node.reality) {
    clashProxy.tls = true;
    clashProxy.reality = true;
    if (node.sni) {
      clashProxy.servername = node.sni;
    }
    if (node.fingerprint) {
      clashProxy.fingerprint = node.fingerprint;
    }
    if (node.publicKey) {
      clashProxy['reality-opts'] = {
        'public-key': node.publicKey,
        'short-id': node.shortId || ''
      };
    }
  }

  // 添加网络配置
  clashProxy.network = node.network || 'tcp';

  console.log('📋 生成的Clash代理配置:');
  console.log(JSON.stringify(clashProxy, null, 2));
  console.log('');

  // 生成完整的Clash配置
  const fullClashConfig = {
    port: 7890,
    'socks-port': 7891,
    'allow-lan': false,
    mode: 'rule',
    'log-level': 'info',
    'external-controller': '127.0.0.1:9090',
    proxies: [clashProxy],
    'proxy-groups': [
      {
        name: '🚀 节点选择',
        type: 'select',
        proxies: ['🎯 全球直连', clashProxy.name]
      },
      {
        name: '🎯 全球直连',
        type: 'select',
        proxies: ['DIRECT']
      }
    ],
    rules: [
      'DOMAIN-SUFFIX,local,DIRECT',
      'IP-CIDR,*********/8,DIRECT',
      'IP-CIDR,**********/12,DIRECT',
      'IP-CIDR,***********/16,DIRECT',
      'IP-CIDR,10.0.0.0/8,DIRECT',
      'GEOIP,CN,🎯 全球直连',
      'MATCH,🚀 节点选择'
    ]
  };

  console.log('📋 完整的Clash配置:');
  console.log(JSON.stringify(fullClashConfig, null, 2));
  console.log('');

  // 验证配置
  console.log('✅ 配置验证:');
  console.log(`- 代理数量: ${fullClashConfig.proxies.length}`);
  console.log(`- 代理组数量: ${fullClashConfig['proxy-groups'].length}`);
  console.log(`- 规则数量: ${fullClashConfig.rules.length}`);
  
  // 检查代理组引用
  const proxyNames = fullClashConfig.proxies.map(p => p.name);
  const groupProxies = fullClashConfig['proxy-groups'][0].proxies;
  const missingProxies = groupProxies.filter(name => 
    name !== 'DIRECT' && name !== '🎯 全球直连' && !proxyNames.includes(name)
  );
  
  if (missingProxies.length === 0) {
    console.log('✅ 代理组引用检查通过');
  } else {
    console.log(`❌ 代理组引用错误: ${missingProxies.join(', ')}`);
  }

  console.log('\n🎉 测试完成！');
  console.log('\n💡 关键信息:');
  console.log('- REALITY 公钥已正确解析');
  console.log('- Flow 控制 (xtls-rprx-vision) 已支持');
  console.log('- SNI 域名 (www.icloud.com) 已配置');
  console.log('- 指纹伪装 (chrome) 已设置');
  console.log('- 生成的配置应该可以在支持REALITY的Clash客户端中正常工作');

} catch (error) {
  console.error('❌ 解析失败:', error.message);
  console.error('详细错误:', error);
}

console.log('\n📝 使用建议:');
console.log('1. 确保使用支持REALITY的Clash客户端 (如 Clash Verge)');
console.log('2. 检查客户端版本是否足够新');
console.log('3. 如果仍有问题，可能是客户端对REALITY的支持有限');
console.log('4. 可以尝试在V2Ray客户端中测试该节点');
