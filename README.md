# 订阅转换器 (Subscription Converter)

一个运行在 Cloudflare Workers 上的节点订阅转换服务，支持多种代理协议和订阅格式。

## 功能特性

- 🔐 **安全认证**: 简单的用户名密码登录系统
- 📱 **响应式界面**: 完全适配桌面、平板、手机的现代化Web界面
- 🎨 **优雅设计**: 直观的用户界面，支持实时通知和动画效果
- 🔄 **多协议支持**: VMess、VLess、Shadowsocks、Trojan、HTTP/HTTPS
- 📋 **多格式输出**: V2Ray、Clash、Surge订阅格式
- ☁️ **云端存储**: 使用Cloudflare KV存储节点数据
- 🚀 **零成本部署**: 完全运行在Cloudflare免费套餐上
- 📲 **移动优化**: 专门为移动设备优化的触摸友好界面

## 支持的节点类型

- **VMess**: V2Ray原生协议
- **VLess**: V2Ray新协议
- **Shadowsocks**: 经典代理协议
- **Trojan**: 基于TLS的代理协议
- **HTTP/HTTPS**: 标准HTTP代理

## 支持的订阅格式

- **V2Ray**: Base64编码的节点链接列表
- **Clash**: YAML格式配置文件
- **Surge**: Surge专用配置格式

## 界面特性

### 🎨 响应式设计
- **桌面端 (1024px+)**: 三列网格布局，最大化屏幕利用率
- **平板端 (768px-1023px)**: 两列网格布局，平衡内容和可读性
- **手机端 (<768px)**: 单列布局，优化触摸操作

### 🔔 智能通知
- ✅ 成功操作时显示绿色确认通知
- ❌ 错误时显示红色警告通知
- 🎬 优雅的滑入滑出动画效果
- ⏰ 自动消失，不干扰用户操作

### 📋 订阅链接管理
- 🔗 一键复制订阅链接到剪贴板
- 📱 移动端优化的复制体验
- 💡 内置使用提示和帮助信息
- 🎯 为每种订阅类型提供专用图标

### 🎛️ 节点管理
- ➕ 直观的节点添加表单
- 🗑️ 安全的节点删除确认
- 📊 清晰的节点信息展示
- 🔄 实时状态更新

## 部署步骤

### 1. 准备工作

确保你有：
- Cloudflare账户
- Node.js 16+ 环境
- Git

### 2. 克隆项目

```bash
git clone <your-repo-url>
cd sub_converter
```

### 3. 安装依赖

```bash
npm install
```

### 4. 配置Cloudflare

1. 登录Cloudflare Dashboard
2. 创建一个新的KV命名空间：
   - 进入 Workers & Pages > KV
   - 点击 "Create a namespace"
   - 命名为 `sub-converter-nodes`
   - 记录命名空间ID

### 5. 配置wrangler.toml

编辑 `wrangler.toml` 文件：

```toml
name = "sub-converter"
main = "src/index.js"
compatibility_date = "2024-01-01"

[vars]
ADMIN_USERNAME = "your-username"    # 修改为你的用户名
ADMIN_PASSWORD = "your-password"    # 修改为你的密码

[[kv_namespaces]]
binding = "NODES_KV"
id = "your-kv-namespace-id"         # 替换为实际的KV命名空间ID
preview_id = "your-preview-kv-namespace-id"  # 可选，用于预览
```

### 6. 部署到Cloudflare

```bash
# 登录Cloudflare
npx wrangler login

# 部署Worker
npm run deploy
```

### 7. 设置自定义域名（可选）

在Cloudflare Dashboard中为你的Worker设置自定义域名。

## 使用方法

### 1. 登录管理面板

访问你的Worker域名，使用配置的用户名和密码登录。

### 2. 添加节点

在管理面板中：
1. 选择节点类型
2. 输入节点名称
3. 粘贴节点链接或配置
4. 点击"添加节点"

### 3. 获取订阅链接

在管理面板中可以看到三种订阅格式的链接：
- V2Ray订阅: `https://your-domain.com/sub/v2ray`
- Clash订阅: `https://your-domain.com/sub/clash`
- Surge订阅: `https://your-domain.com/sub/surge`

### 4. 在客户端中使用

将对应的订阅链接添加到你的代理客户端中。

## 节点链接格式示例

### VMess
```
vmess://eyJ2IjoiMiIsInBzIjoi...
```

### VLess
```
vless://uuid@server:port?encryption=none&type=tcp#name
```

### Shadowsocks
```
ss://method:password@server:port#name
```

### Trojan
```
trojan://password@server:port?sni=domain#name
```

### HTTP/HTTPS
```
*******************************:port
********************************:port
```

## 开发

### 本地开发

```bash
npm run dev
```

### 查看日志

```bash
npm run tail
```

## 安全注意事项

1. **修改默认密码**: 部署前务必修改 `wrangler.toml` 中的用户名和密码
2. **使用HTTPS**: Cloudflare Workers默认提供HTTPS
3. **定期备份**: 定期导出节点数据进行备份
4. **访问控制**: 考虑使用Cloudflare Access进行额外的访问控制

## 故障排除

### 常见问题

1. **KV命名空间错误**: 确保 `wrangler.toml` 中的KV命名空间ID正确
2. **节点解析失败**: 检查节点链接格式是否正确
3. **登录失败**: 确认用户名密码配置正确

### 调试

使用 `npm run tail` 查看Worker运行日志。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！

## 免责声明

本项目仅供学习和研究使用，请遵守当地法律法规。
