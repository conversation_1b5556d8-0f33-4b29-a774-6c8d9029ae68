# 故障排除指南

## 🔧 Clash 配置问题

### 问题：`proxy group[1]: 'use' or 'proxies' missing`

**症状**: 在Clash客户端导入订阅时出现此错误

**原因**: 代理组配置中缺少必要的`proxies`字段或字段为空

**解决方案**:
1. ✅ **已修复** - 更新到v1.1.1版本
2. 确保至少添加了一个有效节点
3. 检查节点配置是否正确

**技术细节**:
- 修复了空节点列表时的配置生成
- 添加了完整的代理组配置验证
- 实现了正确的YAML格式输出

### 问题：Clash配置格式错误

**症状**: Clash提示配置文件格式不正确

**解决方案**:
1. 确保使用最新版本的订阅转换器
2. 检查生成的YAML格式是否正确
3. 验证代理配置字段是否完整

## 📋 订阅链接问题

### 问题：复制链接显示 `{{BASE_URL}}`

**症状**: 点击复制按钮时复制的是模板变量而不是实际URL

**解决方案**:
1. ✅ **已修复** - 更新到v1.1.0版本
2. 刷新页面重新尝试
3. 检查浏览器是否支持剪贴板API

### 问题：订阅链接无法访问

**症状**: 客户端无法下载订阅内容

**解决方案**:
1. 检查Worker是否正常部署
2. 验证域名是否正确
3. 确认网络连接正常
4. 检查防火墙设置

## 🔐 认证问题

### 问题：登录失败

**症状**: 输入正确的用户名密码仍然无法登录

**解决方案**:
1. 检查`wrangler.toml`中的用户名密码配置
2. 确认修改后重新部署了Worker
3. 清除浏览器缓存和Cookie
4. 检查是否有特殊字符需要转义

### 问题：频繁要求重新登录

**症状**: 登录后很快就被要求重新登录

**解决方案**:
1. 检查浏览器Cookie设置
2. 确认系统时间正确
3. 检查是否在无痕模式下使用

## 📱 界面问题

### 问题：移动端显示异常

**症状**: 在手机或平板上界面布局混乱

**解决方案**:
1. ✅ **已修复** - 更新到v1.1.0版本
2. 刷新页面
3. 检查浏览器兼容性
4. 尝试横屏/竖屏切换

### 问题：按钮无法点击

**症状**: 复制按钮或删除按钮没有响应

**解决方案**:
1. 检查浏览器JavaScript是否启用
2. 查看浏览器控制台是否有错误
3. 尝试刷新页面
4. 检查网络连接

## 🚀 部署问题

### 问题：KV命名空间错误

**症状**: 部署时提示KV命名空间不存在

**解决方案**:
1. 使用`npm run setup`自动创建KV命名空间
2. 手动创建KV命名空间并更新`wrangler.toml`
3. 检查Cloudflare账户权限

### 问题：部署失败

**症状**: `wrangler deploy`命令执行失败

**解决方案**:
1. 检查是否已登录Cloudflare: `npx wrangler whoami`
2. 重新登录: `npx wrangler login`
3. 检查网络连接
4. 验证`wrangler.toml`配置

## 🔄 节点管理问题

### 问题：节点添加失败

**症状**: 提交节点配置后显示错误

**解决方案**:
1. 检查节点链接格式是否正确
2. 确认选择了正确的节点类型
3. 验证链接是否完整
4. 查看浏览器控制台错误信息

**支持的格式示例**:
```
VMess: vmess://eyJ2IjoiMiIsInBzIjoi...
VLess: vless://uuid@server:port?params#name
SS: ss://method:password@server:port#name
Trojan: trojan://password@server:port?params#name
```

### 问题：节点删除失败

**症状**: 点击删除按钮没有反应或报错

**解决方案**:
1. 刷新页面重试
2. 检查网络连接
3. 查看浏览器控制台错误
4. 确认登录状态

## 📊 性能问题

### 问题：页面加载缓慢

**症状**: 管理面板打开很慢

**解决方案**:
1. 检查网络连接速度
2. 清除浏览器缓存
3. 减少节点数量
4. 检查Cloudflare Workers状态

### 问题：订阅更新缓慢

**症状**: 客户端更新订阅需要很长时间

**解决方案**:
1. 检查节点数量是否过多
2. 优化节点配置
3. 检查网络环境
4. 考虑使用CDN加速

## 🛠️ 调试技巧

### 查看Worker日志
```bash
npm run tail
```

### 本地调试
```bash
npm run dev
```

### 检查配置
```bash
npx wrangler whoami
npx wrangler kv:namespace list
```

### 浏览器调试
1. 打开开发者工具 (F12)
2. 查看Console标签页的错误信息
3. 检查Network标签页的请求状态
4. 查看Application标签页的Cookie和存储

## 📞 获取帮助

如果以上解决方案都无法解决问题，请：

1. 📝 查看详细文档: `README.md`
2. 🔍 搜索已知问题: `CHANGELOG.md`
3. 🐛 提交Bug报告: [GitHub Issues](your-repo-issues-url)
4. 💬 社区讨论: [GitHub Discussions](your-repo-discussions-url)

提交问题时请包含：
- 错误信息截图
- 浏览器和版本信息
- 操作步骤
- 预期结果和实际结果
