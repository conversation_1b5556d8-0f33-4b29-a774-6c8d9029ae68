/**
 * 简单的测试脚本 - 验证节点转换功能
 */

// 由于这是ES模块，我们需要动态导入
async function runTests() {
  console.log('🧪 开始测试节点转换功能...\n');

  // 测试数据
  const testNodes = [
    {
      type: 'vmess',
      config: 'vmess://eyJ2IjoiMiIsInBzIjoi6K+V6K+V6IqC54K5IiwiYWRkIjoiZXhhbXBsZS5jb20iLCJwb3J0IjoiNDQzIiwiaWQiOiIxMjM0NTY3OC0xMjM0LTEyMzQtMTIzNC0xMjM0NTY3ODkwYWIiLCJhaWQiOiIwIiwic2N5IjoiYXV0byIsIm5ldCI6IndzIiwidHlwZSI6Im5vbmUiLCJob3N0IjoiZXhhbXBsZS5jb20iLCJwYXRoIjoiL3BhdGgiLCJ0bHMiOiJ0bHMifQ=='
    },
    {
      type: 'ss',
      config: 'ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@example.com:8388#测试SS节点'
    },
    {
      type: 'trojan',
      config: 'trojan://<EMAIL>:443?sni=example.com#测试Trojan节点'
    }
  ];

  // 模拟NodeConverter类（简化版）
  class TestNodeConverter {
    parseNode(config, type) {
      console.log(`📝 解析 ${type.toUpperCase()} 节点...`);
      
      try {
        switch (type) {
          case 'vmess':
            if (config.startsWith('vmess://')) {
              const data = JSON.parse(atob(config.substring(8)));
              console.log(`   ✅ 服务器: ${data.add}:${data.port}`);
              console.log(`   ✅ 协议: ${data.net}, TLS: ${data.tls}`);
              return {
                type: 'vmess',
                name: data.ps || 'VMess节点',
                server: data.add,
                port: parseInt(data.port),
                uuid: data.id
              };
            }
            break;
            
          case 'ss':
            if (config.startsWith('ss://')) {
              const url = new URL(config);
              console.log(`   ✅ 服务器: ${url.hostname}:${url.port}`);
              return {
                type: 'ss',
                name: decodeURIComponent(url.hash.substring(1)) || 'SS节点',
                server: url.hostname,
                port: parseInt(url.port)
              };
            }
            break;
            
          case 'trojan':
            if (config.startsWith('trojan://')) {
              const url = new URL(config);
              console.log(`   ✅ 服务器: ${url.hostname}:${url.port}`);
              return {
                type: 'trojan',
                name: decodeURIComponent(url.hash.substring(1)) || 'Trojan节点',
                server: url.hostname,
                port: parseInt(url.port)
              };
            }
            break;
        }
        
        throw new Error('不支持的节点格式');
      } catch (e) {
        console.log(`   ❌ 解析失败: ${e.message}`);
        return null;
      }
    }

    toV2raySubscription(nodes) {
      console.log('\n📋 生成 V2Ray 订阅...');
      const validNodes = nodes.filter(Boolean);
      console.log(`   ✅ 包含 ${validNodes.length} 个有效节点`);
      return btoa(validNodes.map(n => `${n.type}://example-config`).join('\n'));
    }

    toClashConfig(nodes) {
      console.log('\n📋 生成 Clash 配置...');
      const validNodes = nodes.filter(Boolean);
      console.log(`   ✅ 包含 ${validNodes.length} 个代理节点`);
      return {
        proxies: validNodes.map(node => ({
          name: node.name,
          type: node.type,
          server: node.server,
          port: node.port
        }))
      };
    }
  }

  // 运行测试
  const converter = new TestNodeConverter();
  const parsedNodes = [];

  console.log('🔍 测试节点解析...');
  console.log('==================');
  
  for (const testNode of testNodes) {
    const parsed = converter.parseNode(testNode.config, testNode.type);
    if (parsed) {
      parsedNodes.push(parsed);
    }
    console.log('');
  }

  console.log('🔄 测试订阅生成...');
  console.log('==================');
  
  // 测试V2Ray订阅
  const v2raySubscription = converter.toV2raySubscription(parsedNodes);
  console.log(`V2Ray订阅长度: ${v2raySubscription.length} 字符`);

  // 测试Clash配置
  const clashConfig = converter.toClashConfig(parsedNodes);
  console.log(`Clash配置包含 ${clashConfig.proxies.length} 个代理`);

  console.log('\n✅ 所有测试完成！');
  console.log('\n💡 提示：');
  console.log('- 使用 npm run dev 启动本地开发服务器');
  console.log('- 使用 npm run setup 快速部署到 Cloudflare');
  console.log('- 查看 README.md 了解详细使用说明');
}

// 运行测试
runTests().catch(console.error);
