/**
 * 测试新的Clash配置格式
 */

// 导入转换器
const { NodeConverter } = require('./src/converter.js');

// 测试VLESS-REALITY节点
const testVlessLink = 'vless://7983e0bb-1ef9-49c5-c41b-e9d1d8040e0f@185.148.13.75:56879?type=tcp&security=reality&sni=www.icloud.com&pbk=g1f1wLjim5gOVGnI5LGUV0dL4iFXPoiepOPZfSxJe14&flow=xtls-rprx-vision&fp=chrome#US-CloudSilk|<EMAIL>';

console.log('🧪 测试新的Clash配置格式\n');

try {
  const converter = new NodeConverter();
  
  // 解析节点
  const node = converter.parseVless(testVlessLink);
  console.log('✅ 节点解析成功');
  
  // 生成Clash配置
  const clashConfig = converter.toClashConfig([node]);
  
  console.log('📋 新的Clash配置结构:');
  console.log('');
  
  // 基础配置
  console.log('🔧 基础配置:');
  console.log(`   port: ${clashConfig.port}`);
  console.log(`   socks-port: ${clashConfig['socks-port']}`);
  console.log(`   mode: ${clashConfig.mode}`);
  console.log(`   unified-delay: ${clashConfig['unified-delay']}`);
  console.log('');
  
  // DNS配置
  console.log('🌐 DNS配置:');
  console.log(`   enable: ${clashConfig.dns.enable}`);
  console.log(`   ipv6: ${clashConfig.dns.ipv6}`);
  console.log(`   enhanced-mode: ${clashConfig.dns['enhanced-mode']}`);
  console.log(`   fake-ip-range: ${clashConfig.dns['fake-ip-range']}`);
  console.log(`   nameserver: [${clashConfig.dns.nameserver.join(', ')}]`);
  console.log(`   fallback: [${clashConfig.dns.fallback.join(', ')}]`);
  console.log('');
  
  // 代理配置
  console.log('🚀 代理配置:');
  console.log(`   代理数量: ${clashConfig.proxies.length}`);
  if (clashConfig.proxies.length > 0) {
    const proxy = clashConfig.proxies[0];
    console.log(`   示例代理: ${proxy.name}`);
    console.log(`   类型: ${proxy.type}`);
    console.log(`   服务器: ${proxy.server}:${proxy.port}`);
    console.log(`   REALITY: ${proxy.reality || false}`);
    console.log(`   Flow: ${proxy.flow || 'none'}`);
  }
  console.log('');
  
  // 代理组配置
  console.log('📊 代理组配置:');
  console.log(`   代理组数量: ${clashConfig['proxy-groups'].length}`);
  clashConfig['proxy-groups'].forEach((group, index) => {
    console.log(`   ${index + 1}. ${group.name} (${group.type})`);
    console.log(`      代理: [${group.proxies.slice(0, 3).join(', ')}${group.proxies.length > 3 ? '...' : ''}]`);
  });
  console.log('');
  
  // 规则配置
  console.log('📋 规则配置:');
  console.log(`   规则数量: ${clashConfig.rules.length}`);
  console.log('   规则分类:');
  
  const ruleCategories = {
    '广告拦截': clashConfig.rules.filter(rule => rule.includes('🛑 拦截广告')).length,
    '爱奇艺&哔哩哔哩': clashConfig.rules.filter(rule => rule.includes('🌏 爱奇艺&哔哩哔哩')).length,
    '动画疯': clashConfig.rules.filter(rule => rule.includes('📺 动画疯')).length,
    'Steam': clashConfig.rules.filter(rule => rule.includes('Steam')).length,
    'Cloudflare': clashConfig.rules.filter(rule => rule.includes('🌩️ Cloudflare')).length,
    'OneDrive': clashConfig.rules.filter(rule => rule.includes('☁️ OneDrive')).length,
    '国内网站': clashConfig.rules.filter(rule => rule.includes('🇨🇳 国内网站')).length,
    '本地网络': clashConfig.rules.filter(rule => rule.includes('DIRECT') && rule.includes('IP-CIDR')).length
  };
  
  Object.entries(ruleCategories).forEach(([category, count]) => {
    if (count > 0) {
      console.log(`      ${category}: ${count} 条规则`);
    }
  });
  console.log('');
  
  // 验证配置完整性
  console.log('✅ 配置验证:');
  
  // 检查必要字段
  const requiredFields = ['port', 'socks-port', 'mode', 'dns', 'proxies', 'proxy-groups', 'rules'];
  const missingFields = requiredFields.filter(field => !clashConfig[field]);
  
  if (missingFields.length === 0) {
    console.log('   ✅ 所有必要字段都存在');
  } else {
    console.log(`   ❌ 缺少字段: ${missingFields.join(', ')}`);
  }
  
  // 检查代理组引用
  const proxyNames = clashConfig.proxies.map(p => p.name);
  const allGroupProxies = clashConfig['proxy-groups'].flatMap(group => group.proxies);
  const invalidRefs = allGroupProxies.filter(name => 
    name !== 'DIRECT' && name !== 'REJECT' && !proxyNames.includes(name)
  );
  
  if (invalidRefs.length === 0) {
    console.log('   ✅ 代理组引用检查通过');
  } else {
    console.log(`   ❌ 无效引用: ${invalidRefs.join(', ')}`);
  }
  
  // 检查规则引用
  const groupNames = clashConfig['proxy-groups'].map(g => g.name);
  const ruleTargets = clashConfig.rules.map(rule => rule.split(',')[2]).filter(Boolean);
  const invalidRuleTargets = ruleTargets.filter(target => 
    target !== 'DIRECT' && target !== 'REJECT' && !groupNames.includes(target)
  );
  
  if (invalidRuleTargets.length === 0) {
    console.log('   ✅ 规则引用检查通过');
  } else {
    console.log(`   ❌ 无效规则目标: ${[...new Set(invalidRuleTargets)].join(', ')}`);
  }
  
  console.log('\n🎉 新的Clash配置格式测试完成！');
  
  console.log('\n💡 新配置特性:');
  console.log('✅ 完整的DNS配置 (fake-ip模式)');
  console.log('✅ 统一延迟测试');
  console.log('✅ 详细的代理组分类');
  console.log('✅ 丰富的分流规则');
  console.log('✅ 广告拦截规则');
  console.log('✅ 国内外网站分流');
  console.log('✅ 特定服务优化 (Steam, OneDrive等)');
  console.log('✅ VLESS-REALITY完整支持');

} catch (error) {
  console.error('❌ 测试失败:', error.message);
  console.error('详细错误:', error);
}

console.log('\n📝 配置说明:');
console.log('- 🔰 选择节点: 主要的节点选择组');
console.log('- 🌏 爱奇艺&哔哩哔哩: 国内视频网站直连');
console.log('- 📺 动画疯: 台湾动画网站代理');
console.log('- 🎮 Steam: Steam平台分流优化');
console.log('- 🌩️ Cloudflare: Cloudflare服务');
console.log('- ☁️ OneDrive: 微软云存储');
console.log('- 🇨🇳 国内网站: 国内网站直连');
console.log('- 🛑 拦截广告: 广告拦截');
console.log('- 🐟 漏网之鱼: 兜底规则');
