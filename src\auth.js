// 认证相关功能

export class Auth {
  constructor(env) {
    this.env = env;
  }

  // 验证用户凭据
  validateCredentials(username, password) {
    return username === this.env.ADMIN_USERNAME && password === this.env.ADMIN_PASSWORD;
  }

  // 生成JWT token (简化版)
  generateToken(username) {
    const payload = {
      username,
      exp: Date.now() + (24 * 60 * 60 * 1000) // 24小时过期
    };
    return btoa(JSON.stringify(payload));
  }

  // 验证token
  validateToken(token) {
    try {
      const payload = JSON.parse(atob(token));
      if (payload.exp < Date.now()) {
        return false; // token过期
      }
      return payload.username === this.env.ADMIN_USERNAME;
    } catch (e) {
      return false;
    }
  }

  // 从请求中获取token
  getTokenFromRequest(request) {
    const cookie = request.headers.get('Cookie');
    if (!cookie) return null;
    
    const match = cookie.match(/auth_token=([^;]+)/);
    return match ? match[1] : null;
  }

  // 检查是否已认证
  isAuthenticated(request) {
    const token = this.getTokenFromRequest(request);
    return token && this.validateToken(token);
  }

  // 创建认证cookie
  createAuthCookie(token) {
    return `auth_token=${token}; HttpOnly; Secure; SameSite=Strict; Max-Age=86400; Path=/`;
  }

  // 创建登出cookie
  createLogoutCookie() {
    return `auth_token=; HttpOnly; Secure; SameSite=Strict; Max-Age=0; Path=/`;
  }
}
