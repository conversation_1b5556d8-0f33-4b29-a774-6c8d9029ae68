# 节点配置示例

本文档提供各种类型节点的配置示例，帮助你快速添加节点到订阅转换器中。

## VMess 节点示例

### 基本 VMess 节点
```
vmess://eyJ2IjoiMiIsInBzIjoi6aaZ5rivMDEiLCJhZGQiOiJoay5leGFtcGxlLmNvbSIsInBvcnQiOiI0NDMiLCJpZCI6IjEyMzQ1Njc4LTEyMzQtMTIzNC0xMjM0LTEyMzQ1Njc4OTBhYiIsImFpZCI6IjAiLCJzY3kiOiJhdXRvIiwibmV0Ijoid3MiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiJoay5leGFtcGxlLmNvbSIsInBhdGgiOiIvcGF0aCIsInRscyI6InRscyJ9
```

### VMess + WebSocket + TLS
```
vmess://eyJ2IjoiMiIsInBzIjoi6aaZ5rivV1MrVExTIiwiYWRkIjoiaGsuZXhhbXBsZS5jb20iLCJwb3J0IjoiNDQzIiwiaWQiOiIxMjM0NTY3OC0xMjM0LTEyMzQtMTIzNC0xMjM0NTY3ODkwYWIiLCJhaWQiOiIwIiwic2N5IjoiYXV0byIsIm5ldCI6IndzIiwidHlwZSI6Im5vbmUiLCJob3N0IjoiaGsuZXhhbXBsZS5jb20iLCJwYXRoIjoiL3dzIiwidGxzIjoidGxzIn0=
```

## VLess 节点示例

### 基本 VLess 节点
```
vless://<EMAIL>:443?encryption=none&type=tcp&security=tls#VLess节点
```

### VLess + WebSocket + TLS
```
vless://<EMAIL>:443?encryption=none&type=ws&path=/ws&host=example.com&security=tls#VLess-WS-TLS
```

### VLess + gRPC + TLS
```
vless://<EMAIL>:443?encryption=none&type=grpc&serviceName=grpcservice&security=tls#VLess-gRPC
```

## Shadowsocks 节点示例

### 基本 SS 节点
```
ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@example.com:8388#SS节点
```

### SS with SIP003 Plugin
```
ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@example.com:8388/?plugin=v2ray-plugin%3Bmode%3Dwebsocket%3Bhost%3Dexample.com%3Bpath%3D%2Fws#SS-V2Ray-Plugin
```

### 不同加密方式的 SS 节点
```
# AES-256-GCM
ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ=@example.com:8388#SS-AES-256-GCM

# ChaCha20-Poly1305
ss://Y2hhY2hhMjAtcG9seTEzMDU6cGFzc3dvcmQ=@example.com:8388#SS-ChaCha20

# AES-128-GCM
ss://YWVzLTEyOC1nY206cGFzc3dvcmQ=@example.com:8388#SS-AES-128-GCM
```

## Trojan 节点示例

### 基本 Trojan 节点
```
trojan://<EMAIL>:443?sni=example.com#Trojan节点
```

### Trojan + WebSocket
```
trojan://<EMAIL>:443?type=ws&path=/ws&host=example.com&sni=example.com#Trojan-WS
```

### Trojan + gRPC
```
trojan://<EMAIL>:443?type=grpc&serviceName=grpcservice&sni=example.com#Trojan-gRPC
```

## HTTP/HTTPS 代理示例

### HTTP 代理
```
http://username:<EMAIL>:8080
```

### HTTPS 代理
```
https://username:<EMAIL>:8443
```

### 无认证的 HTTP 代理
```
http://proxy.example.com:8080
```

## 节点链接解码说明

### VMess 链接格式
VMess 链接是 Base64 编码的 JSON 配置，解码后的格式如下：
```json
{
  "v": "2",
  "ps": "节点名称",
  "add": "服务器地址",
  "port": "端口",
  "id": "UUID",
  "aid": "额外ID",
  "scy": "加密方式",
  "net": "传输协议",
  "type": "伪装类型",
  "host": "伪装域名",
  "path": "路径",
  "tls": "TLS设置"
}
```

### VLess 链接格式
VLess 使用 URL 格式：
```
vless://UUID@服务器:端口?参数=值&参数=值#节点名称
```

常用参数：
- `encryption`: 加密方式（通常为 none）
- `type`: 传输协议（tcp, ws, grpc 等）
- `security`: 安全传输（none, tls）
- `path`: WebSocket 路径
- `host`: 伪装域名
- `serviceName`: gRPC 服务名

### Shadowsocks 链接格式
```
ss://Base64编码的方法:密码@服务器:端口#节点名称
```

Base64 编码前的格式：`加密方法:密码`

### Trojan 链接格式
```
trojan://密码@服务器:端口?参数=值#节点名称
```

常用参数：
- `sni`: SNI 域名
- `type`: 传输协议
- `path`: WebSocket 路径
- `host`: 伪装域名

## 添加节点的步骤

1. **选择正确的节点类型**：根据你的节点链接选择对应的类型
2. **输入节点名称**：给节点起一个容易识别的名字
3. **粘贴节点配置**：将完整的节点链接粘贴到配置框中
4. **点击添加**：系统会自动解析并保存节点

## 常见问题

### Q: 为什么我的节点添加失败？
A: 请检查：
- 节点类型是否选择正确
- 节点链接是否完整
- 链接格式是否符合标准

### Q: 支持哪些传输协议？
A: 目前支持：
- TCP
- WebSocket (WS)
- gRPC
- HTTP/2

### Q: 如何测试节点是否有效？
A: 添加节点后，可以：
1. 生成订阅链接
2. 在客户端中导入测试
3. 检查连接状态

### Q: 可以批量导入节点吗？
A: 目前需要逐个添加节点，未来版本会考虑添加批量导入功能。
